body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    '<PERSON>bunt<PERSON>', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom CSS classes to replace Tailwind */
.min-h-screen { min-height: 100vh; }
.bg-gradient-to-br { background: linear-gradient(to bottom right, #dcfce7, #dbeafe); }
.from-green-100 { /* handled by bg-gradient-to-br */ }
.to-blue-100 { /* handled by bg-gradient-to-br */ }
.font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif; }
.text-gray-900 { color: #111827; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.py-10 { padding-top: 2.5rem; padding-bottom: 2.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.w-full { width: 100%; }
.max-w-4xl { max-width: 56rem; }
.justify-between { justify-content: space-between; }
.mb-10 { margin-bottom: 2.5rem; }
.p-4 { padding: 1rem; }
.bg-white { background-color: #ffffff; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.font-extrabold { font-weight: 800; }
.text-green-700 { color: #15803d; }
.mr-3 { margin-right: 0.75rem; }
.text-green-500 { color: #22c55e; }
.bg-gray-200 { background-color: #e5e7eb; }
.hover\:bg-gray-300:hover { background-color: #d1d5db; }
.text-gray-800 { color: #1f2937; }
.font-semibold { font-weight: 600; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.rounded-full { border-radius: 9999px; }
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1); }
.focus\:ring-gray-400:focus { box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.5); }
.mr-2 { margin-right: 0.5rem; }
.md\:flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.p-6 { padding: 1.5rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.md\:w-1\/2 { width: 50%; }
.mb-6 { margin-bottom: 1.5rem; }
.md\:mb-0 { margin-bottom: 0; }
.md\:pr-8 { padding-right: 2rem; }
.rounded-lg { border-radius: 0.5rem; }
.h-auto { height: auto; }
.object-cover { object-fit: cover; }
.text-center { text-align: center; }
.md\:text-left { text-align: left; }
.mb-4 { margin-bottom: 1rem; }
.leading-tight { line-height: 1.25; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-gray-600 { color: #4b5563; }
.leading-relaxed { line-height: 1.625; }
.md\:justify-start { justify-content: flex-start; }
.text-5xl { font-size: 3rem; line-height: 1; }
.font-bold { font-weight: 700; }
.text-green-600 { color: #16a34a; }
.bg-green-600 { background-color: #16a34a; }
.hover\:bg-green-700:hover { background-color: #15803d; }
.text-white { color: #ffffff; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.duration-300 { transition-duration: 300ms; }
.hover\:scale-105:hover { transform: scale(1.05); }
.focus\:ring-4:focus { box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1); }
.focus\:ring-green-300:focus { box-shadow: 0 0 0 4px rgba(134, 239, 172, 0.5); }
.inline-block { display: inline-block; }
.ml-2 { margin-left: 0.5rem; }
.max-w-2xl { max-width: 42rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.block { display: block; }
.text-gray-700 { color: #374151; }
.relative { position: relative; }
.absolute { position: absolute; }
.left-3 { left: 0.75rem; }
.top-1\/2 { top: 50%; }
.-translate-y-1\/2 { transform: translateY(-50%); }
.text-gray-400 { color: #9ca3af; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.appearance-none { appearance: none; }
.border { border-width: 1px; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.px-10 { padding-left: 2.5rem; padding-right: 2.5rem; }
.leading-tight { line-height: 1.25; }
.focus\:ring-green-400:focus { box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }
.border-red-500 { border-color: #ef4444; }
.border-gray-300 { border-color: #d1d5db; }
.text-red-500 { color: #ef4444; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.italic { font-style: italic; }
.mt-1 { margin-top: 0.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.bg-gray-300 { background-color: #d1d5db; }
.hover\:bg-gray-400:hover { background-color: #9ca3af; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.focus\:ring-gray-200:focus { box-shadow: 0 0 0 3px rgba(229, 231, 235, 0.5); }
.border-b { border-bottom-width: 1px; }
.pb-4 { padding-bottom: 1rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-spin { animation: spin 1s linear infinite; }
.rounded-full { border-radius: 9999px; }
.h-8 { height: 2rem; }
.w-8 { width: 2rem; }
.border-b-2 { border-bottom-width: 2px; }
.border-gray-900 { border-color: #111827; }
.text-gray-500 { color: #6b7280; }
.text-yellow-500 { color: #eab308; }
.mb-8 { margin-bottom: 2rem; }
.bg-purple-600 { background-color: #9333ea; }
.hover\:bg-purple-700:hover { background-color: #7c3aed; }
.focus\:ring-purple-300:focus { box-shadow: 0 0 0 4px rgba(196, 181, 253, 0.5); }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.bg-blue-600 { background-color: #2563eb; }
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.focus\:ring-blue-300:focus { box-shadow: 0 0 0 4px rgba(147, 197, 253, 0.5); }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.bg-black { background-color: #000000; }
.bg-opacity-50 { background-color: rgba(0, 0, 0, 0.5); }
.z-50 { z-index: 50; }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.max-w-sm { max-width: 24rem; }
.scale-105 { transform: scale(1.05); }
.animate-fade-in { animation: fadeIn 0.3s ease-in-out; }
.text-blue-500 { color: #3b82f6; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0, -30px, 0); }
  70% { transform: translate3d(0, -15px, 0); }
  90% { transform: translate3d(0, -4px, 0); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

/* Responsive utilities */
@media (min-width: 768px) {
  .md\:flex-row { flex-direction: row; }
  .md\:w-1\/2 { width: 50%; }
  .md\:mb-0 { margin-bottom: 0; }
  .md\:pr-8 { padding-right: 2rem; }
  .md\:text-left { text-align: left; }
  .md\:justify-start { justify-content: flex-start; }
}
