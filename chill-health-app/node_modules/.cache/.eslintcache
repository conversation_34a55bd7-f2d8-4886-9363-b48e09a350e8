[{"/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/src/index.js": "1", "/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/src/App.js": "2", "/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/src/reportWebVitals.js": "3"}, {"size": 535, "mtime": 1751786403792, "results": "4", "hashOfConfig": "5"}, {"size": 19966, "mtime": 1751786878817, "results": "6", "hashOfConfig": "5"}, {"size": 362, "mtime": 1751786403793, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ldg2ht", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/src/index.js", [], [], "/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/src/App.js", [], [], "/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/src/reportWebVitals.js", [], []]