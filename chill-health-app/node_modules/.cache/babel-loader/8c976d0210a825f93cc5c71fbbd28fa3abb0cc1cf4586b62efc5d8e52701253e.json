{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 4v16\",\n  key: \"8vvj80\"\n}], [\"path\", {\n  d: \"M17 4v16\",\n  key: \"7dpous\"\n}], [\"path\", {\n  d: \"M19 4H9.5a4.5 4.5 0 0 0 0 9H13\",\n  key: \"sh4n9v\"\n}]];\nconst Pilcrow = createLucideIcon(\"pilcrow\", __iconNode);\nexport { __iconNode, Pilcrow as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/node_modules/lucide-react/src/icons/pilcrow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13 4v16', key: '8vvj80' }],\n  ['path', { d: 'M17 4v16', key: '7dpous' }],\n  ['path', { d: 'M19 4H9.5a4.5 4.5 0 0 0 0 9H13', key: 'sh4n9v' }],\n];\n\n/**\n * @component @name Pilcrow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgNHYxNiIgLz4KICA8cGF0aCBkPSJNMTcgNHYxNiIgLz4KICA8cGF0aCBkPSJNMTkgNEg5LjVhNC41IDQuNSAwIDAgMCAwIDlIMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pilcrow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pilcrow = createLucideIcon('pilcrow', __iconNode);\n\nexport default Pilcrow;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAU,GACjE;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}