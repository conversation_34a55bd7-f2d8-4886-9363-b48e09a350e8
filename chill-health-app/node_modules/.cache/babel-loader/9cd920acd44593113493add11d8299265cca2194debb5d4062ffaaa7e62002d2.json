{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2.5 16.88a1 1 0 0 1-.32-1.43l9-13.02a1 1 0 0 1 1.64 0l9 13.01a1 1 0 0 1-.32 1.44l-8.51 4.86a2 2 0 0 1-1.98 0Z\",\n  key: \"aenxs0\"\n}], [\"path\", {\n  d: \"M12 2v20\",\n  key: \"t6zp3m\"\n}]];\nconst Pyramid = createLucideIcon(\"pyramid\", __iconNode);\nexport { __iconNode, Pyramid as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Pyramid", "createLucideIcon"], "sources": ["/Users/<USER>/Developer/food-apps/shill-health-nutrition-product/chill-health-app/node_modules/lucide-react/src/icons/pyramid.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.5 16.88a1 1 0 0 1-.32-1.43l9-13.02a1 1 0 0 1 1.64 0l9 13.01a1 1 0 0 1-.32 1.44l-8.51 4.86a2 2 0 0 1-1.98 0Z',\n      key: 'aenxs0',\n    },\n  ],\n  ['path', { d: 'M12 2v20', key: 't6zp3m' }],\n];\n\n/**\n * @component @name Pyramid\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi41IDE2Ljg4YTEgMSAwIDAgMS0uMzItMS40M2w5LTEzLjAyYTEgMSAwIDAgMSAxLjY0IDBsOSAxMy4wMWExIDEgMCAwIDEtLjMyIDEuNDRsLTguNTEgNC44NmEyIDIgMCAwIDEtMS45OCAwWiIgLz4KICA8cGF0aCBkPSJNMTIgMnYyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pyramid\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pyramid = createLucideIcon('pyramid', __iconNode);\n\nexport default Pyramid;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}