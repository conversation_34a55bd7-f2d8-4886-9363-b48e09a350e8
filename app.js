import React, { useState, useEffect, createContext, useContext } from 'react';
import { Package, Globe, CheckCircle, XCircle, ShoppingBag, MapPin, Phone, User, DollarSign, CreditCard } from 'lucide-react';

// Language Context
const LanguageContext = createContext();

// Language Provider Component
const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en'); // 'en' for English, 'zh' for Chinese

  const toggleLanguage = () => {
    setLanguage(prevLang => (prevLang === 'en' ? 'zh' : 'en'));
  };

  const translations = {
    en: {
      appName: "Chill Health",
      tagline: "Your Healthy Lunch Solution",
      productName: "Chill Health Nutrition Meal",
      productDescription: "A balanced and delicious meal designed for local people's lunch. Packed with essential nutrients, fresh ingredients, and a taste that satisfies. Perfect for a quick, healthy, and convenient lunch option.",
      price: "RM 15.00",
      orderNow: "Order Now",
      quantity: "Quantity",
      name: "Name",
      address: "Delivery Address",
      phone: "Phone Number",
      placeOrder: "Place Order",
      orderSummary: "Order Summary",
      total: "Total",
      confirmOrder: "Confirm Order",
      shopperConfirmationTitle: "Shopper Confirmation",
      shopperConfirmationMessage: "Your order has been received and is awaiting confirmation from our shopper. You will receive an update shortly!",
      paymentCheckoutTitle: "Payment Checkout",
      paymentInstructions: "Please proceed with your payment.",
      payNow: "Pay Now",
      paymentSuccessTitle: "Payment Successful!",
      paymentSuccessMessage: "Thank you for your purchase! Your order has been confirmed and will be delivered soon.",
      backToHome: "Back to Home",
      requiredField: "This field is required.",
      invalidPhone: "Please enter a valid phone number.",
      shopperConfirmed: "Shopper Confirmed!",
      shopperConfirmedMessage: "Your order has been confirmed by the shopper and is now being prepared for delivery!",
      orderPlaced: "Order Placed!",
    },
    zh: {
      appName: "清凉健康",
      tagline: "您的健康午餐方案",
      productName: "清凉健康营养餐",
      productDescription: "专为本地人午餐设计的均衡美味餐点。富含必需营养素、新鲜食材，口感令人满意。是快速、健康、便捷午餐的完美选择。",
      price: "RM 15.00",
      orderNow: "立即订购",
      quantity: "数量",
      name: "姓名",
      address: "送货地址",
      phone: "电话号码",
      placeOrder: "下订单",
      orderSummary: "订单摘要",
      total: "总计",
      confirmOrder: "确认订单",
      shopperConfirmationTitle: "商家确认",
      shopperConfirmationMessage: "您的订单已收到，正在等待商家确认。您将很快收到更新！",
      paymentCheckoutTitle: "支付结算",
      paymentInstructions: "请完成您的支付。",
      payNow: "立即支付",
      paymentSuccessTitle: "支付成功！",
      paymentSuccessMessage: "感谢您的购买！您的订单已确认，即将送达。",
      backToHome: "返回首页",
      requiredField: "此字段为必填项。",
      invalidPhone: "请输入有效的电话号码。",
      shopperConfirmed: "商家已确认！",
      shopperConfirmedMessage: "您的订单已由商家确认，正在准备发货！",
      orderPlaced: "订单已下达！",
    }
  };

  const t = translations[language];

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom Input Component for consistent styling and validation
const Input = ({ id, label, type = 'text', value, onChange, icon: Icon, placeholder, error }) => {
  return (
    <div className="mb-4">
      <label htmlFor={id} className="block text-gray-700 text-sm font-bold mb-2">
        {label}
      </label>
      <div className="relative">
        {Icon && <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />}
        <input
          type={type}
          id={id}
          name={id}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={`shadow appearance-none border rounded-lg w-full py-3 px-10 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition duration-200 ${error ? 'border-red-500' : 'border-gray-300'}`}
        />
      </div>
      {error && <p className="text-red-500 text-xs italic mt-1">{error}</p>}
    </div>
  );
};

// Main App Component
const App = () => {
  const { language, toggleLanguage, t } = useContext(LanguageContext);
  const [currentStep, setCurrentStep] = useState('product'); // 'product', 'orderForm', 'orderSummary', 'shopperConfirmation', 'paymentCheckout', 'paymentSuccess'
  const [order, setOrder] = useState({
    quantity: 1,
    name: '',
    address: '',
    phone: '',
    total: 0,
  });
  const [errors, setErrors] = useState({});
  const [shopperConfirmed, setShopperConfirmed] = useState(false);
  const [showShopperConfirmationModal, setShowShopperConfirmationModal] = useState(false);
  const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false);

  // Firebase setup (not used for persistence in this example, but included as per instructions)
  const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
  const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};
  const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

  useEffect(() => {
    // Calculate total whenever quantity changes
    setOrder(prevOrder => ({
      ...prevOrder,
      total: prevOrder.quantity * 15.00 // Assuming RM 15.00 per product
    }));
  }, [order.quantity]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setOrder(prevOrder => ({
      ...prevOrder,
      [name]: value
    }));
    // Clear error for the field as user types
    if (errors[name]) {
      setErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateOrderForm = () => {
    let newErrors = {};
    if (!order.name.trim()) {
      newErrors.name = t.requiredField;
    }
    if (!order.address.trim()) {
      newErrors.address = t.requiredField;
    }
    if (!order.phone.trim()) {
      newErrors.phone = t.requiredField;
    } else if (!/^\d{7,15}$/.test(order.phone)) { // Simple phone number validation (7-15 digits)
      newErrors.phone = t.invalidPhone;
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePlaceOrder = () => {
    if (validateOrderForm()) {
      setCurrentStep('orderSummary');
    }
  };

  const handleConfirmOrder = () => {
    setCurrentStep('shopperConfirmation');
    // Simulate shopper confirmation after a delay
    setShowShopperConfirmationModal(true);
    setTimeout(() => {
      setShopperConfirmed(true);
      setShowShopperConfirmationModal(false); // Close the initial modal
      // Optionally show a new modal for shopper confirmed
      setTimeout(() => {
        setCurrentStep('paymentCheckout');
      }, 1000); // Small delay before moving to payment
    }, 3000); // Simulate 3 seconds for shopper confirmation
  };

  const handlePayNow = () => {
    setCurrentStep('paymentSuccess');
    setShowPaymentSuccessModal(true);
    setTimeout(() => {
      setShowPaymentSuccessModal(false);
      setCurrentStep('product'); // Reset to home
      setOrder({ quantity: 1, name: '', address: '', phone: '', total: 0 }); // Clear form
      setShopperConfirmed(false); // Reset shopper confirmation
    }, 3000); // Simulate 3 seconds for payment success display
  };

  const renderProductSection = () => (
    <div className="flex flex-col md:flex-row items-center justify-center p-6 bg-white rounded-xl shadow-lg">
      <div className="md:w-1/2 mb-6 md:mb-0 md:pr-8">
        <img
          src="https://placehold.co/600x400/C8E6C9/2E8B57?text=Chill+Health+Meal"
          alt={t.productName}
          className="rounded-lg shadow-md w-full h-auto object-cover"
          onError={(e) => { e.target.onerror = null; e.target.src = "https://placehold.co/600x400/C8E6C9/2E8B57?text=Image+Not+Found"; }}
        />
      </div>
      <div className="md:w-1/2 text-center md:text-left">
        <h2 className="text-4xl font-extrabold text-gray-800 mb-4 leading-tight">{t.productName}</h2>
        <p className="text-lg text-gray-600 mb-6 leading-relaxed">{t.productDescription}</p>
        <div className="flex items-center justify-center md:justify-start mb-6">
          <span className="text-5xl font-bold text-green-600 mr-3">{t.price}</span>
          <Package className="text-green-500" size={40} />
        </div>
        <button
          onClick={() => setCurrentStep('orderForm')}
          className="bg-green-600 hover:bg-green-700 text-white font-bold py-4 px-8 rounded-full shadow-lg transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300"
        >
          {t.orderNow} <ShoppingBag className="inline-block ml-2" size={20} />
        </button>
      </div>
    </div>
  );

  const renderOrderForm = () => (
    <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-2xl mx-auto">
      <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">{t.orderNow}</h2>
      <div className="mb-6">
        <label htmlFor="quantity" className="block text-gray-700 text-sm font-bold mb-2">
          {t.quantity}
        </label>
        <input
          type="number"
          id="quantity"
          name="quantity"
          min="1"
          value={order.quantity}
          onChange={handleInputChange}
          className="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition duration-200"
        />
      </div>
      <Input
        id="name"
        label={t.name}
        type="text"
        value={order.name}
        onChange={handleInputChange}
        icon={User}
        placeholder={t.name}
        error={errors.name}
      />
      <Input
        id="address"
        label={t.address}
        type="text"
        value={order.address}
        onChange={handleInputChange}
        icon={MapPin}
        placeholder={t.address}
        error={errors.address}
      />
      <Input
        id="phone"
        label={t.phone}
        type="tel"
        value={order.phone}
        onChange={handleInputChange}
        icon={Phone}
        placeholder="e.g., 0123456789"
        error={errors.phone}
      />
      <button
        onClick={handlePlaceOrder}
        className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full w-full shadow-lg transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300 mt-6"
      >
        {t.placeOrder}
      </button>
      <button
        onClick={() => setCurrentStep('product')}
        className="mt-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-full w-full shadow-md transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-gray-200"
      >
        {t.backToHome}
      </button>
    </div>
  );

  const renderOrderSummary = () => (
    <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-2xl mx-auto">
      <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">{t.orderSummary}</h2>
      <div className="border-b pb-4 mb-4">
        <p className="text-lg text-gray-700 mb-2"><span className="font-semibold">{t.productName}</span> x {order.quantity}</p>
        <p className="text-lg text-gray-700 mb-2 flex items-center"><User size={20} className="mr-2 text-gray-500" /> <span className="font-semibold">{t.name}:</span> {order.name}</p>
        <p className="text-lg text-gray-700 mb-2 flex items-center"><MapPin size={20} className="mr-2 text-gray-500" /> <span className="font-semibold">{t.address}:</span> {order.address}</p>
        <p className="text-lg text-gray-700 mb-2 flex items-center"><Phone size={20} className="mr-2 text-gray-500" /> <span className="font-semibold">{t.phone}:</span> {order.phone}</p>
      </div>
      <div className="flex justify-between items-center mb-6">
        <p className="text-2xl font-bold text-gray-800">{t.total}:</p>
        <p className="text-3xl font-bold text-green-600">RM {order.total.toFixed(2)}</p>
      </div>
      <button
        onClick={handleConfirmOrder}
        className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full w-full shadow-lg transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300"
      >
        {t.confirmOrder} <CheckCircle className="inline-block ml-2" size={20} />
      </button>
      <button
        onClick={() => setCurrentStep('orderForm')}
        className="mt-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-full w-full shadow-md transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-gray-200"
      >
        {t.backToHome}
      </button>
    </div>
  );

  const renderShopperConfirmation = () => (
    <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-2xl mx-auto text-center">
      <h2 className="text-3xl font-bold text-gray-800 mb-4">{t.shopperConfirmationTitle}</h2>
      {shopperConfirmed ? (
        <>
          <CheckCircle className="text-green-500 mx-auto mb-4" size={64} />
          <p className="text-lg text-gray-700 mb-6">{t.shopperConfirmedMessage}</p>
          <button
            onClick={() => setCurrentStep('paymentCheckout')}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-full w-full shadow-lg transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-green-300"
          >
            {t.payNow} <CreditCard className="inline-block ml-2" size={20} />
          </button>
        </>
      ) : (
        <>
          <ShoppingBag className="text-blue-500 mx-auto mb-4 animate-bounce" size={64} />
          <p className="text-lg text-gray-700 mb-6">{t.shopperConfirmationMessage}</p>
          <div className="text-gray-500 text-sm">
            {/* Simple loading indicator */}
            <div className="flex justify-center items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mr-3"></div>
              <span>{t.orderPlaced}</span>
            </div>
          </div>
        </>
      )}
    </div>
  );

  const renderPaymentCheckout = () => (
    <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-2xl mx-auto text-center">
      <h2 className="text-3xl font-bold text-gray-800 mb-4">{t.paymentCheckoutTitle}</h2>
      <DollarSign className="text-yellow-500 mx-auto mb-4" size={64} />
      <p className="text-lg text-gray-700 mb-6">{t.paymentInstructions}</p>
      <p className="text-2xl font-bold text-green-600 mb-8">{t.total}: RM {order.total.toFixed(2)}</p>
      <button
        onClick={handlePayNow}
        className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-full w-full shadow-lg transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-300"
      >
        {t.payNow} <CreditCard className="inline-block ml-2" size={20} />
      </button>
    </div>
  );

  const renderPaymentSuccess = () => (
    <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-2xl mx-auto text-center">
      <h2 className="text-3xl font-bold text-gray-800 mb-4">{t.paymentSuccessTitle}</h2>
      <CheckCircle className="text-green-600 mx-auto mb-4 animate-pulse" size={80} />
      <p className="text-lg text-gray-700 mb-6">{t.paymentSuccessMessage}</p>
      <button
        onClick={() => {
          setCurrentStep('product');
          setOrder({ quantity: 1, name: '', address: '', phone: '', total: 0 });
          setShopperConfirmed(false);
        }}
        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full w-full shadow-lg transform transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300"
      >
        {t.backToHome}
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-100 to-blue-100 font-sans text-gray-900 flex flex-col items-center py-10 px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <header className="w-full max-w-4xl flex justify-between items-center mb-10 p-4 bg-white rounded-xl shadow-md">
        <h1 className="text-4xl font-extrabold text-green-700 flex items-center">
          <Package className="mr-3 text-green-500" size={40} />
          {t.appName}
        </h1>
        <button
          onClick={toggleLanguage}
          className="flex items-center bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-full transition duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400"
        >
          <Globe className="mr-2" size={20} />
          {language === 'en' ? '中文' : 'English'}
        </button>
      </header>

      {/* Main Content Area */}
      <main className="w-full max-w-4xl">
        {currentStep === 'product' && renderProductSection()}
        {currentStep === 'orderForm' && renderOrderForm()}
        {currentStep === 'orderSummary' && renderOrderSummary()}
        {currentStep === 'shopperConfirmation' && renderShopperConfirmation()}
        {currentStep === 'paymentCheckout' && renderPaymentCheckout()}
        {currentStep === 'paymentSuccess' && renderPaymentSuccess()}
      </main>

      {/* Modals for confirmation and success messages */}
      {showShopperConfirmationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl p-8 max-w-sm w-full text-center transform scale-105 animate-fade-in">
            <ShoppingBag className="text-blue-500 mx-auto mb-4 animate-bounce" size={64} />
            <h3 className="text-2xl font-bold text-gray-800 mb-3">{t.shopperConfirmationTitle}</h3>
            <p className="text-gray-700 mb-6">{t.shopperConfirmationMessage}</p>
            <div className="flex justify-center items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mr-3"></div>
              <span>{t.orderPlaced}</span>
            </div>
          </div>
        </div>
      )}

      {showPaymentSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl p-8 max-w-sm w-full text-center transform scale-105 animate-fade-in">
            <CheckCircle className="text-green-600 mx-auto mb-4 animate-pulse" size={64} />
            <h3 className="text-2xl font-bold text-gray-800 mb-3">{t.paymentSuccessTitle}</h3>
            <p className="text-gray-700 mb-6">{t.paymentSuccessMessage}</p>
            <button
              onClick={() => setShowPaymentSuccessModal(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-full shadow-md transition duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300"
            >
              OK
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Root component that provides the language context
const Root = () => (
  <LanguageProvider>
    <App />
  </LanguageProvider>
);

export default Root;
